import { defineConfig } from "@playwright/test";

export default defineConfig({
  testDir: "../playwright_run_experiment",
  outputDir: "../playwright_run_experiment/output/artifacts", // videos/screenshots/traces
  use: {
    headless: true,
    video: "on",
    trace: "on",
    screenshot: "only-on-failure",
  },
  reporter: [["html", { outputFolder: "../playwright_run_experiment/output/report", open: "never" }]],
});
