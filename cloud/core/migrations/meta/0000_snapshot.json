{"version": "5", "dialect": "mysql", "id": "aee779c5-db1d-4655-95ec-6451c18455be", "prevId": "********-0000-0000-0000-************", "tables": {"account": {"name": "account", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true, "autoincrement": false}, "time_created": {"name": "time_created", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "time_updated": {"name": "time_updated", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3)"}, "time_deleted": {"name": "time_deleted", "type": "timestamp(3)", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"email": {"name": "email", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraint": {}}, "billing": {"name": "billing", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true, "autoincrement": false}, "workspace_id": {"name": "workspace_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true, "autoincrement": false}, "time_created": {"name": "time_created", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "time_updated": {"name": "time_updated", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3)"}, "time_deleted": {"name": "time_deleted", "type": "timestamp(3)", "primaryKey": false, "notNull": false, "autoincrement": false}, "customer_id": {"name": "customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_method_id": {"name": "payment_method_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_method_last4": {"name": "payment_method_last4", "type": "<PERSON><PERSON><PERSON>(4)", "primaryKey": false, "notNull": false, "autoincrement": false}, "balance": {"name": "balance", "type": "bigint", "primaryKey": false, "notNull": true, "autoincrement": false}, "reload": {"name": "reload", "type": "boolean", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"billing_workspace_id_id_pk": {"name": "billing_workspace_id_id_pk", "columns": ["workspace_id", "id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "payment": {"name": "payment", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true, "autoincrement": false}, "workspace_id": {"name": "workspace_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true, "autoincrement": false}, "time_created": {"name": "time_created", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "time_updated": {"name": "time_updated", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3)"}, "time_deleted": {"name": "time_deleted", "type": "timestamp(3)", "primaryKey": false, "notNull": false, "autoincrement": false}, "customer_id": {"name": "customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_id": {"name": "payment_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "amount": {"name": "amount", "type": "bigint", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"payment_workspace_id_id_pk": {"name": "payment_workspace_id_id_pk", "columns": ["workspace_id", "id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "usage": {"name": "usage", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true, "autoincrement": false}, "workspace_id": {"name": "workspace_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true, "autoincrement": false}, "time_created": {"name": "time_created", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "time_updated": {"name": "time_updated", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3)"}, "time_deleted": {"name": "time_deleted", "type": "timestamp(3)", "primaryKey": false, "notNull": false, "autoincrement": false}, "model": {"name": "model", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "input_tokens": {"name": "input_tokens", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "output_tokens": {"name": "output_tokens", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "reasoning_tokens": {"name": "reasoning_tokens", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "cache_read_tokens": {"name": "cache_read_tokens", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "cache_write_tokens": {"name": "cache_write_tokens", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "cost": {"name": "cost", "type": "bigint", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"usage_workspace_id_id_pk": {"name": "usage_workspace_id_id_pk", "columns": ["workspace_id", "id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "key": {"name": "key", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true, "autoincrement": false}, "workspace_id": {"name": "workspace_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true, "autoincrement": false}, "time_created": {"name": "time_created", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "time_updated": {"name": "time_updated", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3)"}, "time_deleted": {"name": "time_deleted", "type": "timestamp(3)", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "time_used": {"name": "time_used", "type": "timestamp(3)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"global_key": {"name": "global_key", "columns": ["key"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {"key_workspace_id_id_pk": {"name": "key_workspace_id_id_pk", "columns": ["workspace_id", "id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "user": {"name": "user", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true, "autoincrement": false}, "workspace_id": {"name": "workspace_id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true, "autoincrement": false}, "time_created": {"name": "time_created", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "time_updated": {"name": "time_updated", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3)"}, "time_deleted": {"name": "time_deleted", "type": "timestamp(3)", "primaryKey": false, "notNull": false, "autoincrement": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "time_seen": {"name": "time_seen", "type": "timestamp(3)", "primaryKey": false, "notNull": false, "autoincrement": false}, "color": {"name": "color", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"user_email": {"name": "user_email", "columns": ["workspace_id", "email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {"user_workspace_id_id_pk": {"name": "user_workspace_id_id_pk", "columns": ["workspace_id", "id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "workspace": {"name": "workspace", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true, "autoincrement": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "time_created": {"name": "time_created", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(now())"}, "time_updated": {"name": "time_updated", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3)"}, "time_deleted": {"name": "time_deleted", "type": "timestamp(3)", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"slug": {"name": "slug", "columns": ["slug"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {"workspace_id": {"name": "workspace_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"tables": {}, "indexes": {}}}