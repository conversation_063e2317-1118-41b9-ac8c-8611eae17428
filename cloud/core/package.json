{"$schema": "https://json.schemastore.org/package.json", "name": "@opencode/cloud-core", "version": "0.6.3", "private": true, "type": "module", "dependencies": {"@aws-sdk/client-sts": "3.782.0", "@opencode/cloud-resource": "workspace:*", "@planetscale/database": "1.19.0", "drizzle-orm": "0.41.0", "postgres": "3.4.7", "stripe": "18.0.0", "ulid": "3.0.0"}, "exports": {"./*": "./src/*"}, "scripts": {"db": "sst shell drizzle-kit", "typecheck": "tsc --noEmit"}, "devDependencies": {"drizzle-kit": "0.30.5", "mysql2": "3.14.4"}}