{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ESNext", "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "jsx": "preserve", "jsxImportSource": "solid-js", "allowJs": true, "strict": true, "noEmit": true, "types": ["vinxi/types/client"], "isolatedModules": true, "paths": {"~/*": ["./src/*"]}}}