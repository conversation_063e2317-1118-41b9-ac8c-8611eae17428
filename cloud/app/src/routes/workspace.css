[data-page="workspace"] {
  line-height: 1;

  @media (max-width: 30rem) {
    padding: var(--space-4);
    gap: var(--space-5);
  }

  /* Workspace Header */
  [data-component="workspace-header"] {
    position: sticky;
    top: 0;
    z-index: 100;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-4) var(--space-4);
    border-bottom: 1px solid var(--color-border);
    background-color: var(--color-bg);

    @media (max-width: 30rem) {
      padding: 0 var(--space-4);
      margin: calc(-1 * var(--space-4));
      margin-bottom: var(--space-5);
    }
  }

  [data-slot="header-brand"] {
    flex: 0 0 auto;
    padding-top: 4px;

    svg {
      width: 138px;
    }

    [data-component="site-title"] {
      font-size: var(--font-size-lg);
      font-weight: 600;
      color: var(--color-text);
      text-decoration: none;
      letter-spacing: -0.02em;
    }
  }

  [data-slot="header-actions"] {
    display: flex;
    gap: var(--space-4);
    align-items: center;
    font-size: var(--font-size-sm);

    span {
      color: var(--color-text-muted);
    }

    a,
    button {
      appearance: none;
      background: none;
      border: none;
      cursor: pointer;
      padding: 0;
      color: var(--color-text);
      text-decoration: underline;
      text-underline-offset: var(--space-0-75);
      text-decoration-thickness: 1px;
      text-transform: uppercase;
    }
  }
}
