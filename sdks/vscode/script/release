#!/usr/bin/env bash

# Parse command line arguments
minor=false
while [ "$#" -gt 0 ]; do
  case "$1" in
    --minor) minor=true; shift 1;;
    *) echo "Unknown parameter: $1"; exit 1;;
  esac
done

# Get the latest Git tag
git fetch --force --tags
latest_tag=$(git tag --sort=committerdate | grep -E '^vscode-v[0-9]+\.[0-9]+\.[0-9]+$' | tail -1)
if [ -z "$latest_tag" ]; then
    echo "No tags found"
    exit 1
fi

echo "Latest tag: $latest_tag"

# Split the tag into major, minor, and patch numbers
IFS='.' read -ra VERSION <<< "$latest_tag"

if [ "$minor" = true ]; then
    # Increment the minor version and reset patch to 0
    minor_number=${VERSION[1]}
    let "minor_number++"
    new_version="${VERSION[0]}.$minor_number.0"
else
    # Increment the patch version
    patch_number=${VERSION[2]}
    let "patch_number++"
    new_version="${VERSION[0]}.${VERSION[1]}.$patch_number"
fi

echo "New version: $new_version"

# Tag
git tag $new_version
git push --tags