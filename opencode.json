{"$schema": "https://opencode.ai/config.json", "provider": {"oc-frank": {"npm": "@ai-sdk/openai-compatible", "name": "OC-Frank", "options": {"baseURL": "https://api.gateway.frank.dev.opencode.ai/v1"}, "models": {"anthropic/claude-sonnet-4": {"name": "<PERSON> 4"}, "azure/gpt-4.1": {"name": "GPT-4.1"}, "zhipuai/glm-4.5-flash": {"name": "GLM-4.5 Flash"}}}}, "mcp": {"context7": {"type": "remote", "url": "https://mcp.context7.com/sse"}, "weather": {"type": "local", "command": ["opencode", "x", "@h1deya/mcp-server-weather"]}, "playwright-mcp": {"type": "local", "command": ["bun", "x", "playwright-mcp"], "enabled": true, "environment": {"PLAYWRIGHT_BROWSERS_PATH": "0"}}}, "instructions": ["AGENTS.md"]}