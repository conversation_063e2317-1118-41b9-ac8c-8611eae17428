name: Guidelines Check

on:
  # Disabled - uncomment to re-enable
  # pull_request_target:
  #   types: [opened, synchronize]

jobs:
  check-guidelines:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Install opencode
        run: curl -fsSL https://opencode.ai/install | bash

      - name: Check PR guidelines compliance
        env:
          ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          OPENCODE_PERMISSION: '{ "bash": { "gh*": "allow", "gh pr review*": "deny", "*": "deny" } }'
        run: |
          opencode run -m anthropic/claude-sonnet-4-20250514 "A new pull request has been created: '${{ github.event.pull_request.title }}'

          <pr-number>
          ${{ github.event.pull_request.number }}
          </pr-number>

          <pr-description>
          ${{ github.event.pull_request.body }}
          </pr-description>

          Please check all the code changes in this pull request against the guidelines in AGENTS.md file in this repository. Diffs are important but make sure you read the entire file to get proper context. Make it clear the suggestions are merely suggestions and the human can decide what to do

          Use the gh cli to create comments on the files for the violations. Try to leave the comment on the exact line number. If you have a suggested fix include it in a suggestion code block.

          Command MUST be like this.
          ```
          gh api \
            --method POST \
            -H "Accept: application/vnd.github+json" \
            -H "X-GitHub-Api-Version: 2022-11-28" \
            /repos/${{ github.repository }}/pulls/${{ github.event.pull_request.number }}/comments \
            -f 'body=[summary of issue]' -f 'commit_id=${{ github.event.pull_request.head.sha }}' -f 'path=[path-to-file]' -F "line=[line]" -f 'side=RIGHT'
          ```

          Only create comments for actual violations. If the code follows all guidelines, don't run any gh commands."
