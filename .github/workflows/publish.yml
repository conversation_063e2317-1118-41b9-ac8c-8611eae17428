name: publish
run-name: "${{ format('v{0}', inputs.version) }}"

on:
  workflow_dispatch:
    inputs:
      version:
        description: "Version to publish"
        required: true
        type: string
      title:
        description: "Custom title for this run"
        required: false
        type: string

concurrency: ${{ github.workflow }}-${{ github.ref }}

permissions:
  contents: write
  packages: write

jobs:
  publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - run: git fetch --force --tags

      - uses: actions/setup-go@v5
        with:
          go-version: ">=1.24.0"
          cache: true
          cache-dependency-path: go.sum

      - uses: oven-sh/setup-bun@v2
        with:
          bun-version: 1.2.19

      - name: Cache ~/.bun
        id: cache-bun
        uses: actions/cache@v3
        with:
          path: ~/.bun
          key: ${{ runner.os }}-bun-${{ hashFiles('bun.lock') }}
          restore-keys: |
            ${{ runner.os }}-bun-

      - name: Install makepkg
        run: |
          sudo apt-get update
          sudo apt-get install -y pacman-package-manager
      - name: Setup SSH for AUR
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.AUR_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          git config --global user.email "<EMAIL>"
          git config --global user.name "opencode"
          ssh-keyscan -H aur.archlinux.org >> ~/.ssh/known_hosts || true
      - name: Install dependencies
        run: bun install

      - name: Publish
        run: |
          OPENCODE_VERSION=${{ inputs.version }} ./script/publish.ts
        env:
          GITHUB_TOKEN: ${{ secrets.SST_GITHUB_TOKEN }}
          AUR_KEY: ${{ secrets.AUR_KEY }}
          NPM_CONFIG_TOKEN: ${{ secrets.NPM_TOKEN }}
