import { test, expect } from "@playwright/test"

test("Add a product to the cart and verify cart icon updates", async ({ page }) => {
  // Go to the login page
  await page.goto("https://www.saucedemo.com/")

  // Log in with username and password
  await page.fill('input[data-test="username"]', "standard_user")
  await page.fill('input[data-test="password"]', "secret_sauce")
  await page.click('input[data-test="login-button"]')

  // Wait for inventory page to load
  await expect(page).toHaveURL(/.*inventory/)

  // Verify cart icon is initially empty
  const cartBadge = page.locator(".shopping_cart_badge")
  await expect(cartBadge).toHaveCount(0)

  // Click "Add to cart" for the first product
  await page.click('button[data-test^="add-to-cart-"]')

  // Cart icon should update to reflect "1" item
  await expect(cartBadge).toHaveText("1")
})
