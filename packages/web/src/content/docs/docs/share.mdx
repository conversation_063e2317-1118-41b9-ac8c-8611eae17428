---
title: Share
description: Share your opencode conversations.
---

opencode's share feature allows you to create public links to your opencode conversations, so you can collaborate with teammates or get help from others.

:::note
Shared conversations are publicly accessible to anyone with the link.
:::

---

## How it works

When you share a conversation, opencode:

1. Creates a unique public URL for your session
2. Syncs your conversation history to our servers
3. Makes the conversation accessible via the shareable link — `opencode.ai/s/<share-id>`

---

## Sharing

opencode supports three sharing modes that control how conversations are shared:

---

### Manual (default)

By default, opencode uses manual sharing mode. Sessions are not shared automatically, but you can manually share them using the `/share` command:

```
/share
```

This will generate a unique URL that'll be copied to your clipboard.

To explicitly set manual mode in your [config file](/docs/config):

```json title="opencode.json"
{
  "$schema": "https://opencode.ai/config.json",
  "share": "manual"
}
```

---

### Auto-share

You can enable automatic sharing for all new conversations by setting the `share` option to `"auto"` in your [config file](/docs/config):

```json title="opencode.json"
{
  "$schema": "https://opencode.ai/config.json",
  "share": "auto"
}
```

With auto-share enabled, every new conversation will automatically be shared and a link will be generated.

---

### Disabled

You can disable sharing entirely by setting the `share` option to `"disabled"` in your [config file](/docs/config):

```json title="opencode.json"
{
  "$schema": "https://opencode.ai/config.json",
  "share": "disabled"
}
```

To enforce this across your team for a given project, add it to the `opencode.json` in your project and check into Git.

---

## Un-sharing

To stop sharing a conversation and remove it from public access:

```
/unshare
```

This will remove the share link and delete the data related to the conversation.

---

## Privacy

There are a few things to keep in mind when sharing a conversation.

---

### Data retention

Shared conversations remain accessible until you explicitly unshare them. This
includes:

- Full conversation history
- All messages and responses
- Session metadata

---

### Recommendations

- Only share conversations that don't contain sensitive information.
- Review conversation content before sharing.
- Unshare conversations when collaboration is complete.
- Avoid sharing conversations with proprietary code or confidential data.
- For sensitive projects, disable sharing entirely.

---

## For enterprises

For enterprise deployments, the share feature can be:

- **Disabled** entirely for security compliance
- **Restricted** to users authenticated through SSO only
- **Self-hosted** on your own infrastructure

[Learn more](/docs/enterprise) about using opencode in your organization.
