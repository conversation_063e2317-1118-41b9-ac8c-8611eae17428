---
title: Server
description: Interact with opencode server over HTTP.
---

import config from "../../../../config.mjs"
export const typesUrl = `${config.github}/blob/dev/packages/sdk/js/src/gen/types.gen.ts`

The `opencode serve` command runs a headless HTTP server that exposes an OpenAPI endpoint that an opencode client can use.

---

### Usage

```bash
opencode serve [--port <number>] [--hostname <string>]
```

#### Options

| Flag         | Short | Description           | Default     |
| ------------ | ----- | --------------------- | ----------- |
| `--port`     | `-p`  | Port to listen on     | `4096`      |
| `--hostname` | `-h`  | Hostname to listen on | `127.0.0.1` |

---

### How it works

When you run `opencode` it starts a TUI and a server. Where the TUI is the
client that talks to the server. The server exposes an OpenAPI 3.1 spec
endpoint. This endpoint is also used to generate an [SDK](/docs/sdk).

:::tip
Use the opencode server to interact with opencode programmatically.
:::

This architecture lets opencode support multiple clients and allows you to interact with opencode programmatically.

You can run `opencode serve` to start a standalone server. If you have the
opencode TUI running, `opencode serve` will start a new server.

---

#### Connect to an existing server

When you start the TUI it randomly assigns a port and hostname. You can instead pass in the `--hostname` and `--port` [flags](/docs/cli). Then use this to connect to its server.

The [`/tui`](#tui) endpoint can be used to drive the TUI through the server. For example, you can prefill or run a prompt. This setup is used by the opencode [IDE](/docs/ide) plugins.

---

## Spec

The server publishes an OpenAPI 3.1 spec that can be viewed at:

```
http://<hostname>:<port>/doc
```

For example, `http://localhost:4096/doc`. Use the spec to generate clients or inspect request and response types. Or view it in a Swagger explorer.

---

## APIs

The opencode server exposes the following APIs.

---

### App

| Method | Path        | Description        | Response                                |
| ------ | ----------- | ------------------ | --------------------------------------- |
| `GET`  | `/app`      | Get app info       | <a href={typesUrl}><code>App</code></a> |
| `POST` | `/app/init` | Initialize the app | `boolean`                               |

---

### Config

| Method | Path                | Description                       | Response                                                                                              |
| ------ | ------------------- | --------------------------------- | ----------------------------------------------------------------------------------------------------- |
| `GET`  | `/config`           | Get config info                   | <a href={typesUrl}><code>Config</code></a>                                                            |
| `GET`  | `/config/providers` | List providers and default models | `{ providers: `<a href={typesUrl}>Provider[]</a>`, default: { [key: string]: string } }` |

---

### Sessions

| Method   | Path                                     | Description                        | Notes                                                                                                                                                                      |
| -------- | ---------------------------------------- | ---------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `GET`    | `/session`                               | List sessions                      | Returns <a href={typesUrl}><code>Session[]</code></a>                                                                                                                      |
| `GET`    | `/session/:id`                           | Get session                        | Returns <a href={typesUrl}><code>Session</code></a>                                                                                                                        |
| `GET`    | `/session/:id/children`                  | List child sessions                | Returns <a href={typesUrl}><code>Session[]</code></a>                                                                                                                      |
| `POST`   | `/session`                               | Create session                     | body: `{ parentID?, title? }`, returns <a href={typesUrl}><code>Session</code></a>                                                                                         |
| `DELETE` | `/session/:id`                           | Delete session                     |                                                                                                                                                                            |
| `PATCH`  | `/session/:id`                           | Update session properties          | body: `{ title? }`, returns <a href={typesUrl}><code>Session</code></a>                                                                                                    |
| `POST`   | `/session/:id/init`                      | Analyze app and create `AGENTS.md` | body: `{ messageID, providerID, modelID }`                                                                                                                                 |
| `POST`   | `/session/:id/abort`                     | Abort a running session            |                                                                                                                                                                            |
| `POST`   | `/session/:id/share`                     | Share session                      | Returns <a href={typesUrl}><code>Session</code></a>                                                                                                                        |
| `DELETE` | `/session/:id/share`                     | Unshare session                    | Returns <a href={typesUrl}><code>Session</code></a>                                                                                                                        |
| `POST`   | `/session/:id/summarize`                 | Summarize session                  |                                                                                                                                                                            |
| `GET`    | `/session/:id/message`                   | List messages in a session         | Returns `{ info: `<a href={typesUrl}>Message</a>`, parts: `<a href={typesUrl}>Part[]</a>`}[]`                                                    |
| `GET`    | `/session/:id/message/:messageID`        | Get message details                | Returns `{ info: `<a href={typesUrl}>Message</a>`, parts: `<a href={typesUrl}>Part[]</a>`}`                                                      |
| `POST`   | `/session/:id/message`                   | Send chat message                  | body matches [`ChatInput`](https://github.com/sst/opencode/blob/main/packages/opencode/src/session/index.ts#L358), returns <a href={typesUrl}><code>Message</code></a>     |
| `POST`   | `/session/:id/shell`                     | Run a shell command                | body matches [`CommandInput`](https://github.com/sst/opencode/blob/main/packages/opencode/src/session/index.ts#L1007), returns <a href={typesUrl}><code>Message</code></a> |
| `POST`   | `/session/:id/revert`                    | Revert a message                   | body: `{ messageID }`                                                                                                                                                      |
| `POST`   | `/session/:id/unrevert`                  | Restore reverted messages          |                                                                                                                                                                            |
| `POST`   | `/session/:id/permissions/:permissionID` | Respond to a permission request    | body: `{ response }`                                                                                                                                                       |

---

### Files

| Method | Path                     | Description                  | Response                                                                                    |
| ------ | ------------------------ | ---------------------------- | ------------------------------------------------------------------------------------------- |
| `GET`  | `/find?pattern=<pat>`    | Search for text in files     | Array of match objects with `path`, `lines`, `line_number`, `absolute_offset`, `submatches` |
| `GET`  | `/find/file?query=<q>`   | Find files by name           | `string[]` (file paths)                                                                     |
| `GET`  | `/find/symbol?query=<q>` | Find workspace symbols       | <a href={typesUrl}><code>Symbol[]</code></a>                                                |
| `GET`  | `/file?path=<path>`      | Read a file                  | `{ type: "raw" \| "patch", content: string }`                                               |
| `GET`  | `/file/status`           | Get status for tracked files | <a href={typesUrl}><code>File[]</code></a>                                                  |

---

### Logging

| Method | Path   | Description                                                  | Response  |
| ------ | ------ | ------------------------------------------------------------ | --------- |
| `POST` | `/log` | Write log entry. Body: `{ service, level, message, extra? }` | `boolean` |

---

### Agents

| Method | Path     | Description               | Response                                    |
| ------ | -------- | ------------------------- | ------------------------------------------- |
| `GET`  | `/agent` | List all available agents | <a href={typesUrl}><code>Agent[]</code></a> |

---

### TUI

| Method | Path                    | Description                                 | Response               |
| ------ | ----------------------- | ------------------------------------------- | ---------------------- |
| `POST` | `/tui/append-prompt`    | Append text to the prompt                   | `boolean`              |
| `POST` | `/tui/open-help`        | Open the help dialog                        | `boolean`              |
| `POST` | `/tui/open-sessions`    | Open the session selector                   | `boolean`              |
| `POST` | `/tui/open-themes`      | Open the theme selector                     | `boolean`              |
| `POST` | `/tui/open-models`      | Open the model selector                     | `boolean`              |
| `POST` | `/tui/submit-prompt`    | Submit the current prompt                   | `boolean`              |
| `POST` | `/tui/clear-prompt`     | Clear the prompt                            | `boolean`              |
| `POST` | `/tui/execute-command`  | Execute a command (`{ command }`)           | `boolean`              |
| `POST` | `/tui/show-toast`       | Show toast (`{ title?, message, variant }`) | `boolean`              |
| `GET`  | `/tui/control/next`     | Wait for the next control request           | Control request object |
| `POST` | `/tui/control/response` | Respond to a control request (`{ body }`)   | `boolean`              |

---

### Auth

| Method | Path        | Description                                                     | Response  |
| ------ | ----------- | --------------------------------------------------------------- | --------- |
| `PUT`  | `/auth/:id` | Set authentication credentials. Body must match provider schema | `boolean` |

---

### Events

| Method | Path     | Description                                                                   | Response                  |
| ------ | -------- | ----------------------------------------------------------------------------- | ------------------------- |
| `GET`  | `/event` | Server-sent events stream. First event is `server.connected`, then bus events | Server-sent events stream |

---

### Docs

| Method | Path   | Description                            | Response                                   |
| ------ | ------ | -------------------------------------- | ------------------------------------------ |
| `GET`  | `/doc` | OpenAPI 3.1 specification | HTML page with OpenAPI spec |
