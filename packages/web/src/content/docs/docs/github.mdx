---
title: GitHub
description: Use opencode in GitHub issues and pull-requests.
---

opencode integrates with your GitHub workflow. Mention `/opencode` or `/oc` in your comment, and opencode will execute tasks within your GitHub Actions runner.

---

## Features

- **Triage issues**: Ask opencode to look into an issue and explain it to you.
- **Fix and implement**: Ask opencode to fix an issue or implement a feature. And it will work in a new branch and submits a PR with all the changes.
- **Secure**: opencode runs inside your GitHub's runners.

---

## Installation

Run the following command in a project that is in a GitHub repo:

```bash
opencode github install
```

This will walk you through installing the GitHub app, creating the workflow, and setting up secrets.

---

### Manual Setup

Or you can set it up manually.

1. **Install the GitHub app**

   Head over to [**github.com/apps/opencode-agent**](https://github.com/apps/opencode-agent). Make sure it's installed on the target repository.

2. **Add the workflow**

   Add the following workflow file to `.github/workflows/opencode.yml` in your repo. Make sure to set the appropriate `model` and required API keys in `env`.

   ```yml title=".github/workflows/opencode.yml" {24,26}
   name: opencode

   on:
     issue_comment:
       types: [created]

   jobs:
     opencode:
       if: |
         contains(github.event.comment.body, '/oc') ||
         contains(github.event.comment.body, '/opencode')
       runs-on: ubuntu-latest
       permissions:
         id-token: write
       steps:
         - name: Checkout repository
           uses: actions/checkout@v4
           with:
             fetch-depth: 1

         - name: Run opencode
           uses: sst/opencode/github@latest
           env:
             ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
           with:
             model: anthropic/claude-sonnet-4-20250514
             # share: true
             # github_token: xxxx
   ```

3. **Store the API keys in secrets**

   In your organization or project **settings**, expand **Secrets and variables** on the left and select **Actions**. And add the required API keys.

---

## Configuration

- `model`: The model to use with opencode. Takes the format of `provider/model`. This is **required**.
- `share`: Whether to share the opencode session. Defaults to **true** for public repositories.
- `token`: Optional GitHub access token for performing operations such as creating comments, commiting changes, and opening pull requests. By default, opencode uses the installation access token from the opencode GitHub App, so commits, comments, and pull requests appear as coming from the app.

  Alternatively, you can use the GitHub Action runner's [built-in `GITHUB_TOKEN`](https://docs.github.com/en/actions/tutorials/authenticate-with-github_token) without installing the opencode GitHub App. Just make sure to grant the required permissions in your workflow:

  ```yaml
  permissions:
    id-token: write
    contents: write
    pull-requests: write
    issues: write
  ```

  You can also use a [personal access tokens](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens)(PAT) if preferred.

---

## Examples

Here are some examples of how you can use opencode in GitHub.

- **Explain an issue**

  Add this comment in a GitHub issue.

  ```
  /opencode explain this issue
  ```

  opencode will read the entire thread, including all comments, and reply with a clear explanation.

- **Fix an issue**

  In a GitHub issue, say:

  ```
  /opencode fix this
  ```

  And opencode will create a new branch, implement the changes, and open a PR with the changes.

- **Review PRs and make changes**

  Leave the following comment on a GitHub PR.

  ```
  Delete the attachment from S3 when the note is removed /oc
  ```

  opencode will implement the requested change and commit it to the same PR.
