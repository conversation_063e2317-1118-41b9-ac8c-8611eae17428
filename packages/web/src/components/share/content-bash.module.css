.root {
  display: contents;

  [data-slot="expand-button"] {
    flex: 0 0 auto;
    padding: 2px 0;
    font-size: 0.75rem;
  }

  [data-slot="body"] {
    border: 1px solid var(--sl-color-divider);
    border-radius: 0.25rem;
    overflow: hidden;
    width: 100%;
  }

  [data-slot="header"] {
    position: relative;
    border-bottom: 1px solid var(--sl-color-divider);
    width: 100%;
    height: 1.625rem;
    text-align: center;
    padding: 0 3.25rem;

    > span {
      max-width: min(100%, 140ch);
      display: inline-block;
      white-space: nowrap;
      overflow: hidden;
      line-height: 1.625rem;
      font-size: 0.75rem;
      text-overflow: ellipsis;
      color: var(--sl-color-text-dimmed);
    }

    &::before {
      content: "";
      position: absolute;
      pointer-events: none;
      top: 8px;
      left: 10px;
      width: 2rem;
      height: 0.5rem;
      line-height: 0;
      background-color: var(--sl-color-hairline);
      mask-image: var(--term-icon);
      mask-repeat: no-repeat;
    }
  }

  [data-slot="content"] {
    display: flex;
    flex-direction: column;
    padding: 0.5rem calc(0.5rem + 3px);

    pre {
      --shiki-dark-bg: var(--sl-color-bg) !important;
      background-color: var(--sl-color-bg) !important;
      line-height: 1.6;
      font-size: 0.75rem;
      white-space: pre-wrap;
      word-break: break-word;
      margin: 0;

      span {
        white-space: break-spaces;
      }
    }
  }

  [data-slot="output"] {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 10;
    line-clamp: 10;
    overflow: hidden;
  }

  &[data-expanded] [data-slot="output"] {
    display: block;
    -webkit-line-clamp: none;
    line-clamp: none;
    overflow: visible;
  }
}
