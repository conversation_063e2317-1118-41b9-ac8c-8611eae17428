{"$schema": "https://opencode.ai/theme.json", "defs": {"darkBg": "#282c34", "darkBgAlt": "#21252b", "darkBgPanel": "#353b45", "darkFg": "#abb2bf", "darkFgMuted": "#5c6370", "darkPurple": "#c678dd", "darkBlue": "#61afef", "darkRed": "#e06c75", "darkGreen": "#98c379", "darkYellow": "#e5c07b", "darkOrange": "#d19a66", "darkCyan": "#56b6c2", "lightBg": "#fafafa", "lightBgAlt": "#f0f0f1", "lightBgPanel": "#eaeaeb", "lightFg": "#383a42", "lightFgMuted": "#a0a1a7", "lightPurple": "#a626a4", "lightBlue": "#4078f2", "lightRed": "#e45649", "lightGreen": "#50a14f", "lightYellow": "#c18401", "lightOrange": "#986801", "lightCyan": "#0184bc"}, "theme": {"primary": {"dark": "darkBlue", "light": "lightBlue"}, "secondary": {"dark": "dark<PERSON><PERSON>ple", "light": "lightP<PERSON>ple"}, "accent": {"dark": "dark<PERSON>yan", "light": "lightCyan"}, "error": {"dark": "darkRed", "light": "lightRed"}, "warning": {"dark": "<PERSON><PERSON><PERSON><PERSON>", "light": "light<PERSON>ellow"}, "success": {"dark": "<PERSON><PERSON><PERSON>", "light": "lightGreen"}, "info": {"dark": "darkOrange", "light": "lightOrange"}, "text": {"dark": "darkFg", "light": "lightFg"}, "textMuted": {"dark": "darkFgMuted", "light": "lightFgMuted"}, "background": {"dark": "darkBg", "light": "lightBg"}, "backgroundPanel": {"dark": "darkBgAlt", "light": "lightBgAlt"}, "backgroundElement": {"dark": "darkBgPanel", "light": "lightBgPanel"}, "border": {"dark": "#393f4a", "light": "#d1d1d2"}, "borderActive": {"dark": "darkBlue", "light": "lightBlue"}, "borderSubtle": {"dark": "#2c313a", "light": "#e0e0e1"}, "diffAdded": {"dark": "<PERSON><PERSON><PERSON>", "light": "lightGreen"}, "diffRemoved": {"dark": "darkRed", "light": "lightRed"}, "diffContext": {"dark": "darkFgMuted", "light": "lightFgMuted"}, "diffHunkHeader": {"dark": "dark<PERSON>yan", "light": "lightCyan"}, "diffHighlightAdded": {"dark": "#aad482", "light": "#489447"}, "diffHighlightRemoved": {"dark": "#e8828b", "light": "#d65145"}, "diffAddedBg": {"dark": "#2c382b", "light": "#eafbe9"}, "diffRemovedBg": {"dark": "#3a2d2f", "light": "#fce9e8"}, "diffContextBg": {"dark": "darkBgAlt", "light": "lightBgAlt"}, "diffLineNumber": {"dark": "#495162", "light": "#c9c9ca"}, "diffAddedLineNumberBg": {"dark": "#283427", "light": "#e1f3df"}, "diffRemovedLineNumberBg": {"dark": "#36292b", "light": "#f5e2e1"}, "markdownText": {"dark": "darkFg", "light": "lightFg"}, "markdownHeading": {"dark": "dark<PERSON><PERSON>ple", "light": "lightP<PERSON>ple"}, "markdownLink": {"dark": "darkBlue", "light": "lightBlue"}, "markdownLinkText": {"dark": "dark<PERSON>yan", "light": "lightCyan"}, "markdownCode": {"dark": "<PERSON><PERSON><PERSON>", "light": "lightGreen"}, "markdownBlockQuote": {"dark": "darkFgMuted", "light": "lightFgMuted"}, "markdownEmph": {"dark": "<PERSON><PERSON><PERSON><PERSON>", "light": "light<PERSON>ellow"}, "markdownStrong": {"dark": "darkOrange", "light": "lightOrange"}, "markdownHorizontalRule": {"dark": "darkFgMuted", "light": "lightFgMuted"}, "markdownListItem": {"dark": "darkBlue", "light": "lightBlue"}, "markdownListEnumeration": {"dark": "dark<PERSON>yan", "light": "lightCyan"}, "markdownImage": {"dark": "darkBlue", "light": "lightBlue"}, "markdownImageText": {"dark": "dark<PERSON>yan", "light": "lightCyan"}, "markdownCodeBlock": {"dark": "darkFg", "light": "lightFg"}, "syntaxComment": {"dark": "darkFgMuted", "light": "lightFgMuted"}, "syntaxKeyword": {"dark": "dark<PERSON><PERSON>ple", "light": "lightP<PERSON>ple"}, "syntaxFunction": {"dark": "darkBlue", "light": "lightBlue"}, "syntaxVariable": {"dark": "darkRed", "light": "lightRed"}, "syntaxString": {"dark": "<PERSON><PERSON><PERSON>", "light": "lightGreen"}, "syntaxNumber": {"dark": "darkOrange", "light": "lightOrange"}, "syntaxType": {"dark": "<PERSON><PERSON><PERSON><PERSON>", "light": "light<PERSON>ellow"}, "syntaxOperator": {"dark": "dark<PERSON>yan", "light": "lightCyan"}, "syntaxPunctuation": {"dark": "darkFg", "light": "lightFg"}}}