{"$schema": "https://opencode.ai/theme.json", "defs": {"matrixInk0": "#0a0e0a", "matrixInk1": "#0e130d", "matrixInk2": "#141c12", "matrixInk3": "#1e2a1b", "rainGreen": "#2eff6a", "rainGreenDim": "#1cc24b", "rainGreenHi": "#62ff94", "rainCyan": "#00efff", "rainTeal": "#24f6d9", "rainPurple": "#c770ff", "rainOrange": "#ffa83d", "alertRed": "#ff4b4b", "alertYellow": "#e6ff57", "alertBlue": "#30b3ff", "rainGray": "#8ca391", "lightBg": "#eef3ea", "lightPaper": "#e4ebe1", "lightInk1": "#dae1d7", "lightText": "#203022", "lightGray": "#748476"}, "theme": {"primary": {"dark": "rainGreen", "light": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "secondary": {"dark": "rainCyan", "light": "rainTeal"}, "accent": {"dark": "rainPurple", "light": "rainPurple"}, "error": {"dark": "alertRed", "light": "alertRed"}, "warning": {"dark": "alertYellow", "light": "alertYellow"}, "success": {"dark": "rainGreenHi", "light": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "info": {"dark": "alertBlue", "light": "alertBlue"}, "text": {"dark": "rainGreenHi", "light": "lightText"}, "textMuted": {"dark": "rainGray", "light": "lightGray"}, "background": {"dark": "matrixInk0", "light": "lightBg"}, "backgroundPanel": {"dark": "matrixInk1", "light": "lightPaper"}, "backgroundElement": {"dark": "matrixInk2", "light": "lightInk1"}, "border": {"dark": "matrixInk3", "light": "lightGray"}, "borderActive": {"dark": "rainGreen", "light": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "borderSubtle": {"dark": "matrixInk2", "light": "lightInk1"}, "diffAdded": {"dark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "light": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "diffRemoved": {"dark": "alertRed", "light": "alertRed"}, "diffContext": {"dark": "rainGray", "light": "lightGray"}, "diffHunkHeader": {"dark": "alertBlue", "light": "alertBlue"}, "diffHighlightAdded": {"dark": "#77ffaf", "light": "#5dac7e"}, "diffHighlightRemoved": {"dark": "#ff7171", "light": "#d53a3a"}, "diffAddedBg": {"dark": "#132616", "light": "#e0efde"}, "diffRemovedBg": {"dark": "#261212", "light": "#f9e5e5"}, "diffContextBg": {"dark": "matrixInk1", "light": "lightPaper"}, "diffLineNumber": {"dark": "matrixInk3", "light": "lightGray"}, "diffAddedLineNumberBg": {"dark": "#0f1b11", "light": "#d6e7d2"}, "diffRemovedLineNumberBg": {"dark": "#1b1414", "light": "#f2d2d2"}, "markdownText": {"dark": "rainGreenHi", "light": "lightText"}, "markdownHeading": {"dark": "rainCyan", "light": "rainTeal"}, "markdownLink": {"dark": "alertBlue", "light": "alertBlue"}, "markdownLinkText": {"dark": "rainTeal", "light": "rainTeal"}, "markdownCode": {"dark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "light": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "markdownBlockQuote": {"dark": "rainGray", "light": "lightGray"}, "markdownEmph": {"dark": "rainOrange", "light": "rainOrange"}, "markdownStrong": {"dark": "alertYellow", "light": "alertYellow"}, "markdownHorizontalRule": {"dark": "rainGray", "light": "lightGray"}, "markdownListItem": {"dark": "alertBlue", "light": "alertBlue"}, "markdownListEnumeration": {"dark": "rainTeal", "light": "rainTeal"}, "markdownImage": {"dark": "alertBlue", "light": "alertBlue"}, "markdownImageText": {"dark": "rainTeal", "light": "rainTeal"}, "markdownCodeBlock": {"dark": "rainGreenHi", "light": "lightText"}, "syntaxComment": {"dark": "rainGray", "light": "lightGray"}, "syntaxKeyword": {"dark": "rainPurple", "light": "rainPurple"}, "syntaxFunction": {"dark": "alertBlue", "light": "alertBlue"}, "syntaxVariable": {"dark": "rainGreenHi", "light": "lightText"}, "syntaxString": {"dark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "light": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "syntaxNumber": {"dark": "rainOrange", "light": "rainOrange"}, "syntaxType": {"dark": "alertYellow", "light": "alertYellow"}, "syntaxOperator": {"dark": "rainTeal", "light": "rainTeal"}, "syntaxPunctuation": {"dark": "rainGreenHi", "light": "lightText"}}}