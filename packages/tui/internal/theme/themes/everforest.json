{"$schema": "https://opencode.ai/theme.json", "defs": {"darkStep1": "#2d353b", "darkStep2": "#333c43", "darkStep3": "#343f44", "darkStep4": "#3d484d", "darkStep5": "#475258", "darkStep6": "#7a8478", "darkStep7": "#859289", "darkStep8": "#9da9a0", "darkStep9": "#a7c080", "darkStep10": "#83c092", "darkStep11": "#7a8478", "darkStep12": "#d3c6aa", "darkRed": "#e67e80", "darkOrange": "#e69875", "darkGreen": "#a7c080", "darkCyan": "#83c092", "darkYellow": "#dbbc7f", "lightStep1": "#fdf6e3", "lightStep2": "#efebd4", "lightStep3": "#f4f0d9", "lightStep4": "#efebd4", "lightStep5": "#e6e2cc", "lightStep6": "#a6b0a0", "lightStep7": "#939f91", "lightStep8": "#829181", "lightStep9": "#8da101", "lightStep10": "#35a77c", "lightStep11": "#a6b0a0", "lightStep12": "#5c6a72", "lightRed": "#f85552", "lightOrange": "#f57d26", "lightGreen": "#8da101", "lightCyan": "#35a77c", "lightYellow": "#dfa000"}, "theme": {"primary": {"dark": "darkStep9", "light": "lightStep9"}, "secondary": {"dark": "#7fbbb3", "light": "#3a94c5"}, "accent": {"dark": "#d699b6", "light": "#df69ba"}, "error": {"dark": "darkRed", "light": "lightRed"}, "warning": {"dark": "darkOrange", "light": "lightOrange"}, "success": {"dark": "<PERSON><PERSON><PERSON>", "light": "lightGreen"}, "info": {"dark": "dark<PERSON>yan", "light": "lightCyan"}, "text": {"dark": "darkStep12", "light": "lightStep12"}, "textMuted": {"dark": "darkStep11", "light": "lightStep11"}, "background": {"dark": "darkStep1", "light": "lightStep1"}, "backgroundPanel": {"dark": "darkStep2", "light": "lightStep2"}, "backgroundElement": {"dark": "darkStep3", "light": "lightStep3"}, "border": {"dark": "darkStep7", "light": "lightStep7"}, "borderActive": {"dark": "darkStep8", "light": "lightStep8"}, "borderSubtle": {"dark": "darkStep6", "light": "lightStep6"}, "diffAdded": {"dark": "#4fd6be", "light": "#1e725c"}, "diffRemoved": {"dark": "#c53b53", "light": "#c53b53"}, "diffContext": {"dark": "#828bb8", "light": "#7086b5"}, "diffHunkHeader": {"dark": "#828bb8", "light": "#7086b5"}, "diffHighlightAdded": {"dark": "#b8db87", "light": "#4db380"}, "diffHighlightRemoved": {"dark": "#e26a75", "light": "#f52a65"}, "diffAddedBg": {"dark": "#20303b", "light": "#d5e5d5"}, "diffRemovedBg": {"dark": "#37222c", "light": "#f7d8db"}, "diffContextBg": {"dark": "darkStep2", "light": "lightStep2"}, "diffLineNumber": {"dark": "darkStep3", "light": "lightStep3"}, "diffAddedLineNumberBg": {"dark": "#1b2b34", "light": "#c5d5c5"}, "diffRemovedLineNumberBg": {"dark": "#2d1f26", "light": "#e7c8cb"}, "markdownText": {"dark": "darkStep12", "light": "lightStep12"}, "markdownHeading": {"dark": "#d699b6", "light": "#df69ba"}, "markdownLink": {"dark": "darkStep9", "light": "lightStep9"}, "markdownLinkText": {"dark": "dark<PERSON>yan", "light": "lightCyan"}, "markdownCode": {"dark": "<PERSON><PERSON><PERSON>", "light": "lightGreen"}, "markdownBlockQuote": {"dark": "<PERSON><PERSON><PERSON><PERSON>", "light": "light<PERSON>ellow"}, "markdownEmph": {"dark": "<PERSON><PERSON><PERSON><PERSON>", "light": "light<PERSON>ellow"}, "markdownStrong": {"dark": "darkOrange", "light": "lightOrange"}, "markdownHorizontalRule": {"dark": "darkStep11", "light": "lightStep11"}, "markdownListItem": {"dark": "darkStep9", "light": "lightStep9"}, "markdownListEnumeration": {"dark": "dark<PERSON>yan", "light": "lightCyan"}, "markdownImage": {"dark": "darkStep9", "light": "lightStep9"}, "markdownImageText": {"dark": "dark<PERSON>yan", "light": "lightCyan"}, "markdownCodeBlock": {"dark": "darkStep12", "light": "lightStep12"}, "syntaxComment": {"dark": "darkStep11", "light": "lightStep11"}, "syntaxKeyword": {"dark": "#d699b6", "light": "#df69ba"}, "syntaxFunction": {"dark": "darkStep9", "light": "lightStep9"}, "syntaxVariable": {"dark": "darkRed", "light": "lightRed"}, "syntaxString": {"dark": "<PERSON><PERSON><PERSON>", "light": "lightGreen"}, "syntaxNumber": {"dark": "darkOrange", "light": "lightOrange"}, "syntaxType": {"dark": "<PERSON><PERSON><PERSON><PERSON>", "light": "light<PERSON>ellow"}, "syntaxOperator": {"dark": "dark<PERSON>yan", "light": "lightCyan"}, "syntaxPunctuation": {"dark": "darkStep12", "light": "lightStep12"}}}